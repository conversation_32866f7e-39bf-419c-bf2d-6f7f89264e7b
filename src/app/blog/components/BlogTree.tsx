'use client'

import Link from 'next/link'
import { useState } from 'react'
import { ChevronDown, ChevronRight, Folder, FileText } from 'lucide-react'

interface FileNode {
  name: string
  path: string
  type: 'file' | 'directory'
  children?: FileNode[]
}

interface BlogTreeProps {
  nodes: FileNode[]
}

function FileTreeNode({ node, level = 0 }: { node: FileNode; level?: number }) {
  const [isExpanded, setIsExpanded] = useState(true)
  const indent = level * 24

  if (node.type === 'directory') {
    return (
      <div
        className="mb-3"
        style={{ paddingLeft: `${indent}px` }}>
        <div
          className="flex items-center mb-2 font-semibold text-lg cursor-pointer hover:bg-gray-50 p-3 rounded-lg transition-colors border border-gray-200"
          onClick={() => setIsExpanded(!isExpanded)}>
          {isExpanded ? (
            <ChevronDown className="w-5 h-5 mr-3 text-gray-600" />
          ) : (
            <ChevronRight className="w-5 h-5 mr-3 text-gray-600" />
          )}
          <Folder className="w-5 h-5 mr-3 text-blue-600" />
          <span className="capitalize text-gray-900">{node.name}</span>
        </div>

        {isExpanded && (
          <div className="relative">
            {node.children?.map((child, index) => (
              <FileTreeNode
                key={index}
                node={child}
                level={level + 1}
              />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div
      className="mb-3"
      style={{ paddingLeft: `${indent}px` }}>
      <Link
        href={`/blog/${node.path}`}
        className="block group">
        <div className="hover:bg-gray-50 p-3 rounded-lg transition-colors cursor-pointer border border-gray-200 group-hover:border-blue-300">
          <div className="flex items-center text-base">
            <FileText className="w-5 h-5 mr-3 text-gray-600 group-hover:text-blue-600" />
            <span className="text-gray-900 group-hover:text-blue-600 font-medium">
              {node.name}
            </span>
          </div>
        </div>
      </Link>
    </div>
  )
}

export function BlogTree({ nodes }: BlogTreeProps) {
  return (
    <div className="space-y-4">
      {nodes.map((node, index) => (
        <FileTreeNode
          key={index}
          node={node}
        />
      ))}
    </div>
  )
}
