import { TableOfContents } from './components/TableOfContents'
import { BackToTop } from './components/BackToTop'

export default function MdxLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="bg-white relative">
      {/* 侧边栏目录 */}
      <TableOfContents />

      {/* Dario Amodei 风格布局 - 学术专业 */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        <article className="w-full">
          {/* 文章内容 - 专业展示 */}
          <div className="prose prose-lg max-w-none">{children}</div>

          {/* 学术风格底部 */}
          <div className="mt-16 pt-8 border-t border-gray-200">
            {/* 底部内容可以在这里添加其他信息 */}
          </div>
        </article>
      </div>

      {/* 浮动的返回顶部按钮 */}
      <BackToTop />
    </div>
  )
}
