import Link from 'next/link'

export default function Home() {
  return (
    <div className="max-w-4xl mx-auto px-6 py-16">
      <div className="mb-16">
        <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-8 leading-tight">
          LinkNote
        </h1>
        <div className="prose max-w-none">
          <p className="text-xl md:text-2xl text-gray-700 mb-8 font-serif leading-relaxed">
            欢迎来到我的个人博客，这里是我记录思考、分享见解和探索知识的地方。
          </p>
          <p className="text-lg text-gray-600 mb-12 font-serif leading-relaxed">
            在这个信息爆炸的时代，我希望通过深度思考和精心整理，为读者提供有价值的内容。
            无论是技术探讨、人文思考，还是生活感悟，我都会以诚待人，用心分享。
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4">
          <Link
            href="/blog"
            className="inline-flex items-center justify-center px-6 py-3 bg-gray-900 text-white font-medium rounded-lg hover:bg-gray-800 transition-colors">
            浏览博客
          </Link>
          <Link
            href="/about"
            className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors">
            了解更多
          </Link>
        </div>
      </div>

      <div className="border-t border-gray-200 pt-16">
        <h2 className="text-2xl font-semibold text-gray-900 mb-8">最新文章</h2>
        <div className="space-y-8">
          <article className="group">
            <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
              <Link href="/blog/welcome">欢迎来到我的博客</Link>
            </h3>
            <p className="text-gray-600 mb-2 font-serif">
              这是我博客的第一篇文章，在这里我想分享一下创建这个博客的初衷和未来的计划...
            </p>
            <time className="text-sm text-gray-500">2025年1月</time>
          </article>

          <article className="group">
            <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
              <Link href="/blog/test-toc">测试文章目录</Link>
            </h3>
            <p className="text-gray-600 mb-2 font-serif">
              这是一篇用于测试文章目录功能的示例文章，展示了如何在长文章中使用目录导航...
            </p>
            <time className="text-sm text-gray-500">2025年1月</time>
          </article>
        </div>

        <div className="mt-12">
          <Link
            href="/blog"
            className="text-blue-600 hover:text-blue-800 font-medium">
            查看所有文章 →
          </Link>
        </div>
      </div>
    </div>
  )
}
