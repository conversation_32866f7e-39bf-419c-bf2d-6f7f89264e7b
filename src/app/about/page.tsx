export default function AboutPage() {
  return (
    <div className="max-w-4xl mx-auto px-6 py-16">
      <div className="prose max-w-none">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8 leading-tight">
          关于我
        </h1>

        <div className="text-lg font-serif text-gray-800 leading-relaxed space-y-6">
          <p>
            欢迎来到我的个人博客。我是一名热爱思考和学习的开发者，
            致力于通过技术和文字记录生活中的点点滴滴。
          </p>

          <p>
            在这个快节奏的时代，我希望能够慢下来，深入思考一些问题，
            并将这些思考整理成文字与大家分享。无论是技术探讨、读书心得，
            还是生活感悟，我都会尽力以真诚的态度与读者交流。
          </p>

          <p>
            这个博客的设计灵感来源于简洁而专业的学术风格，
            希望能为读者提供一个舒适的阅读环境。如果你对我的文章有任何想法或建议，
            欢迎通过邮件与我联系。
          </p>

          <h2 className="text-2xl md:text-3xl font-semibold text-gray-900 mt-12 mb-6 leading-tight">
            联系方式
          </h2>

          <p>如果你想与我交流，可以通过以下方式联系我：</p>

          <ul className="list-disc pl-6 space-y-2">
            <li>邮箱：<EMAIL></li>
            <li>GitHub：github.com/yourusername</li>
            <li>Twitter：@yourusername</li>
          </ul>

          <h2 className="text-2xl md:text-3xl font-semibold text-gray-900 mt-12 mb-6 leading-tight">
            关于这个网站
          </h2>

          <p>
            这个博客使用 Next.js 构建，采用了简洁的设计风格，
            专注于内容的呈现和阅读体验。网站支持 MDX 格式的文章，
            可以在文章中嵌入交互式组件。
          </p>

          <p>如果你对网站的技术实现感兴趣，欢迎查看源代码或与我讨论。</p>
        </div>
      </div>
    </div>
  )
}
